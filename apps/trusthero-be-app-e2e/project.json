{"name": "trusthero-be-app-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["trusthero-be-app"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/trusthero-be-app-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["trusthero-be-app:build", "trusthero-be-app:serve"]}}}