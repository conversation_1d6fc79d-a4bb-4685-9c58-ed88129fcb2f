{"name": "trusthero-be-app", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/trusthero-be-app/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["--node-env=production"], "cwd": "apps/trusthero-be-app"}, "configurations": {"development": {"args": ["--node-env=development"]}}}, "prune-lockfile": {"dependsOn": ["build"], "cache": true, "executor": "@nx/js:prune-lockfile", "outputs": ["{workspaceRoot}/dist/apps/trusthero-be-app/package.json", "{workspaceRoot}/dist/apps/trusthero-be-app/package-lock.json"], "options": {"buildTarget": "build"}}, "copy-workspace-modules": {"dependsOn": ["build"], "cache": true, "outputs": ["{workspaceRoot}/dist/apps/trusthero-be-app/workspace_modules"], "executor": "@nx/js:copy-workspace-modules", "options": {"buildTarget": "build"}}, "prune": {"dependsOn": ["prune-lockfile", "copy-workspace-modules"], "executor": "nx:noop"}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "trusthero-be-app:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "trusthero-be-app:build:development"}, "production": {"buildTarget": "trusthero-be-app:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}