{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug trusthero-be-app with Nx", "runtimeExecutable": "npx", "runtimeArgs": ["nx", "serve", "trusthero-be-app"], "env": {"NODE_OPTIONS": "--inspect=9229"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "sourceMaps": true, "outFiles": ["${workspaceFolder}/apps/trusthero-be-app/dist/**/*.(m|c|)js", "!**/node_modules/**"]}]}